<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قراء القرآن الكريم - استمع إلى التلاوات الجميلة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', '<PERSON><PERSON>', sans-serif;
        }

        .arabic-text {
            font-family: '<PERSON><PERSON>', serif;
        }
    </style>
</head>

<body class="bg-gradient-to-bl from-green-50 to-blue-50 min-h-screen">
    <div x-data="recitersApp()" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-bold text-green-800 mb-4 arabic-text">
                <i class="fas fa-mosque text-green-600 ml-4"></i>
                قراء القرآن الكريم
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                استمع إلى التلاوات الجميلة للقرآن الكريم من قراء مشهورين حول العالم
            </p>
        </header>

        <!-- Loading State -->
        <div x-show="loading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600"></div>
            <p class="mr-4 text-gray-600">جاري التحميل...</p>
        </div>

        <!-- Reciters Grid -->
        <div x-show="!loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <template x-for="reciter in reciters" :key="reciter.id">
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                    @click="goToReciter(reciter)">
                    <div class="p-6 text-center">
                        <!-- Reciter Image -->
                        <div
                            class="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center">
                            <i class="fas fa-user text-white text-3xl" x-show="!reciter.image"></i>
                            <img x-show="reciter.image" :src="reciter.image" :alt="reciter.name"
                                class="w-full h-full rounded-full object-cover">
                        </div>

                        <!-- Reciter Name -->
                        <h3 class="text-xl font-semibold text-gray-800 mb-2 arabic-text" x-text="reciter.arabicName">
                        </h3>
                        <p class="text-sm text-gray-600 mb-1" x-text="reciter.name"></p>
                        <p class="text-sm text-gray-500 mb-4" x-text="reciter.country"></p>

                        <!-- Stats -->
                        <div class="flex justify-center space-x-reverse space-x-4 text-sm text-gray-600">
                            <span><i class="fas fa-book-open ml-1"></i>١١٤ سورة</span>
                        </div>

                        <!-- Action Button -->
                        <button
                            class="mt-4 bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-full transition-colors duration-200">
                            <i class="fas fa-play ml-2"></i>استمع
                        </button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-16 py-8 border-t border-gray-200">
            <p class="text-gray-600">
                <i class="fas fa-heart text-red-500 ml-1"></i>
                صُنع بحب للمجتمع الإسلامي
            </p>
        </footer>
    </div>

    <script>
        function recitersApp() {
            return {
                loading: true,
                reciters: [],

                init() {
                    this.loadReciters();
                },

                async loadReciters() {
                    try {
                        // Popular Quran reciters data
                        this.reciters = [
                            { id: 'ar.alafasy', name: 'Mishary Rashid Alafasy', arabicName: 'مشاري راشد العفاسي', country: 'الكويت', image: null },
                            { id: 'ar.abdurrahmaansudais', name: 'Abdul Rahman Al-Sudais', arabicName: 'عبد الرحمن السديس', country: 'السعودية', image: null },
                            { id: 'ar.shaatree', name: 'Abu Bakr Ash-Shaatri', arabicName: 'أبو بكر الشاطري', country: 'السعودية', image: null },
                            { id: 'ar.mahermuaiqly', name: 'Maher Al Muaiqly', arabicName: 'ماهر المعيقلي', country: 'السعودية', image: null },
                            { id: 'ar.saoodshuraym', name: 'Saood Ash-Shuraym', arabicName: 'سعود الشريم', country: 'السعودية', image: null },
                            { id: 'ar.abdullahbasfar', name: 'Abdullah Basfar', arabicName: 'عبد الله بصفر', country: 'السعودية', image: null },
                            { id: 'ar.abdulbasitmurattal', name: 'Abdul Basit (Murattal)', arabicName: 'عبد الباسط عبد الصمد', country: 'مصر', image: null },
                            { id: 'ar.abdulsamad', name: 'Abdul Samad', arabicName: 'عبد الصمد', country: 'مصر', image: null },
                            { id: 'ar.hanirifai', name: 'Hani Ar-Rifai', arabicName: 'هاني الرفاعي', country: 'السعودية', image: null },
                            { id: 'ar.husary', name: 'Mahmoud Khalil Al-Husary', arabicName: 'محمود خليل الحصري', country: 'مصر', image: null },
                            { id: 'ar.minshawi', name: 'Mohamed Siddiq El-Minshawi', arabicName: 'محمد صديق المنشاوي', country: 'مصر', image: null },
                            { id: 'ar.muhammadayyoub', name: 'Muhammad Ayyub', arabicName: 'محمد أيوب', country: 'السعودية', image: null },
                            { id: 'ar.muhammadjibreel', name: 'Muhammad Jibreel', arabicName: 'محمد جبريل', country: 'السعودية', image: null },
                            { id: 'ar.saadalghamdi', name: 'Saad Al-Ghamdi', arabicName: 'سعد الغامدي', country: 'السعودية', image: null },
                            { id: 'ar.yasser', name: 'Yasser Al-Dosari', arabicName: 'ياسر الدوسري', country: 'السعودية', image: null },
                            { id: 'ar.walk', name: 'Khalid Al-Jalil', arabicName: 'خالد الجليل', country: 'السعودية', image: null },
                            { id: 'ar.parhizgar', name: 'Nasser Al-Qatami', arabicName: 'ناصر القطامي', country: 'السعودية', image: null },
                            { id: 'ar.tablawi', name: 'Muhammad At-Tablawi', arabicName: 'محمد الطبلاوي', country: 'مصر', image: null },
                            { id: 'ar.ahmedajamy', name: 'Ahmed Al-Ajamy', arabicName: 'أحمد العجمي', country: 'السعودية', image: null },
                            { id: 'ar.bandar', name: 'Bandar Baleela', arabicName: 'بندر بليلة', country: 'السعودية', image: null },
                            { id: 'ar.fares', name: 'Fares Abbad', arabicName: 'فارس عباد', country: 'السعودية', image: null },
                            { id: 'ar.ibrahim', name: 'Ibrahim Al-Akhdar', arabicName: 'إبراهيم الأخضر', country: 'السعودية', image: null },
                            { id: 'ar.salah', name: 'Salah Al-Budair', arabicName: 'صلاح البدير', country: 'السعودية', image: null },
                            { id: 'ar.omar', name: 'Omar Al-Kazabri', arabicName: 'عمر الكذابري', country: 'السعودية', image: null },
                            { id: 'ar.warsh', name: 'Warsh (Yassin Al-Jazairi)', arabicName: 'ورش (ياسين الجزائري)', country: 'الجزائر', image: null },
                            { id: 'ar.aziz', name: 'Abdul Aziz Al-Ahmad', arabicName: 'عبد العزيز الأحمد', country: 'السعودية', image: null },
                            { id: 'ar.rifai', name: 'Yasser Ar-Rifai', arabicName: 'ياسر الرفاعي', country: 'السعودية', image: null },
                            { id: 'ar.ghamdi', name: 'Abdullah Al-Ghamdi', arabicName: 'عبد الله الغامدي', country: 'السعودية', image: null },
                            { id: 'ar.bukhatir', name: 'Ahmed Al-Bukhatir', arabicName: 'أحمد بوخاطر', country: 'الإمارات', image: null },
                            { id: 'ar.tawfeeq', name: 'Tawfeeq As-Sayegh', arabicName: 'توفيق الصايغ', country: 'السعودية', image: null }
                        ];

                        this.loading = false;
                    } catch (error) {
                        console.error('Error loading reciters:', error);
                        this.loading = false;
                    }
                },

                goToReciter(reciter) {
                    // Navigate to reciter page with URL parameters
                    const params = new URLSearchParams({
                        id: reciter.id,
                        name: reciter.name,
                        arabicName: reciter.arabicName,
                        country: reciter.country
                    });
                    window.location.href = `reciter.html?${params.toString()}`;
                }
            }
        }
    </script>
</body>

</html>