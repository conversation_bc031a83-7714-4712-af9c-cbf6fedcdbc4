<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quran Reciters - Listen to Beautiful Recitations</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-green-50 to-blue-50 min-h-screen">
    <div x-data="recitersApp()" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-6xl font-bold text-green-800 mb-4">
                <i class="fas fa-mosque text-green-600 mr-4"></i>
                Quran Reciters
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Listen to beautiful recitations of the Holy Quran by renowned reciters from around the world
            </p>
        </header>

        <!-- Loading State -->
        <div x-show="loading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600"></div>
        </div>

        <!-- Reciters Grid -->
        <div x-show="!loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <template x-for="reciter in reciters" :key="reciter.id">
                <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                     @click="goToReciter(reciter)">
                    <div class="p-6 text-center">
                        <!-- Reciter Image -->
                        <div class="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center">
                            <i class="fas fa-user text-white text-3xl" x-show="!reciter.image"></i>
                            <img x-show="reciter.image" :src="reciter.image" :alt="reciter.name" 
                                 class="w-full h-full rounded-full object-cover">
                        </div>
                        
                        <!-- Reciter Name -->
                        <h3 class="text-xl font-semibold text-gray-800 mb-2" x-text="reciter.name"></h3>
                        <p class="text-sm text-gray-500 mb-4" x-text="reciter.country"></p>
                        
                        <!-- Stats -->
                        <div class="flex justify-center space-x-4 text-sm text-gray-600">
                            <span><i class="fas fa-book-open mr-1"></i>114 Surahs</span>
                        </div>
                        
                        <!-- Action Button -->
                        <button class="mt-4 bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-full transition-colors duration-200">
                            <i class="fas fa-play mr-2"></i>Listen
                        </button>
                    </div>
                </div>
            </template>
        </div>

        <!-- Footer -->
        <footer class="text-center mt-16 py-8 border-t border-gray-200">
            <p class="text-gray-600">
                <i class="fas fa-heart text-red-500 mr-1"></i>
                Made with love for the Muslim community
            </p>
        </footer>
    </div>

    <script>
        function recitersApp() {
            return {
                loading: true,
                reciters: [],
                
                init() {
                    this.loadReciters();
                },
                
                async loadReciters() {
                    try {
                        // Popular Quran reciters data
                        this.reciters = [
                            { id: 'ar.alafasy', name: 'Mishary Rashid Alafasy', country: 'Kuwait', image: null },
                            { id: 'ar.abdurrahmaansudais', name: 'Abdul Rahman Al-Sudais', country: 'Saudi Arabia', image: null },
                            { id: 'ar.shaatree', name: 'Abu Bakr Ash-Shaatri', country: 'Saudi Arabia', image: null },
                            { id: 'ar.mahermuaiqly', name: 'Maher Al Muaiqly', country: 'Saudi Arabia', image: null },
                            { id: 'ar.saoodshuraym', name: 'Saood Ash-Shuraym', country: 'Saudi Arabia', image: null },
                            { id: 'ar.abdullahbasfar', name: 'Abdullah Basfar', country: 'Saudi Arabia', image: null },
                            { id: 'ar.abdulbasitmurattal', name: 'Abdul Basit (Murattal)', country: 'Egypt', image: null },
                            { id: 'ar.abdulsamad', name: 'Abdul Samad', country: 'Egypt', image: null },
                            { id: 'ar.hanirifai', name: 'Hani Ar-Rifai', country: 'Saudi Arabia', image: null },
                            { id: 'ar.husary', name: 'Mahmoud Khalil Al-Husary', country: 'Egypt', image: null },
                            { id: 'ar.minshawi', name: 'Mohamed Siddiq El-Minshawi', country: 'Egypt', image: null },
                            { id: 'ar.muhammadayyoub', name: 'Muhammad Ayyub', country: 'Saudi Arabia', image: null },
                            { id: 'ar.muhammadjibreel', name: 'Muhammad Jibreel', country: 'Saudi Arabia', image: null },
                            { id: 'ar.saadalghamdi', name: 'Saad Al-Ghamdi', country: 'Saudi Arabia', image: null },
                            { id: 'ar.yasser', name: 'Yasser Al-Dosari', country: 'Saudi Arabia', image: null },
                            { id: 'ar.walk', name: 'Khalid Al-Jalil', country: 'Saudi Arabia', image: null },
                            { id: 'ar.parhizgar', name: 'Nasser Al-Qatami', country: 'Saudi Arabia', image: null },
                            { id: 'ar.tablawi', name: 'Muhammad At-Tablawi', country: 'Egypt', image: null },
                            { id: 'ar.ahmedajamy', name: 'Ahmed Al-Ajamy', country: 'Saudi Arabia', image: null },
                            { id: 'ar.bandar', name: 'Bandar Baleela', country: 'Saudi Arabia', image: null },
                            { id: 'ar.fares', name: 'Fares Abbad', country: 'Saudi Arabia', image: null },
                            { id: 'ar.ibrahim', name: 'Ibrahim Al-Akhdar', country: 'Saudi Arabia', image: null },
                            { id: 'ar.salah', name: 'Salah Al-Budair', country: 'Saudi Arabia', image: null },
                            { id: 'ar.omar', name: 'Omar Al-Kazabri', country: 'Saudi Arabia', image: null },
                            { id: 'ar.warsh', name: 'Warsh (Yassin Al-Jazairi)', country: 'Algeria', image: null },
                            { id: 'ar.aziz', name: 'Abdul Aziz Al-Ahmad', country: 'Saudi Arabia', image: null },
                            { id: 'ar.rifai', name: 'Yasser Ar-Rifai', country: 'Saudi Arabia', image: null },
                            { id: 'ar.ghamdi', name: 'Abdullah Al-Ghamdi', country: 'Saudi Arabia', image: null },
                            { id: 'ar.bukhatir', name: 'Ahmed Al-Bukhatir', country: 'UAE', image: null },
                            { id: 'ar.tawfeeq', name: 'Tawfeeq As-Sayegh', country: 'Saudi Arabia', image: null }
                        ];
                        
                        this.loading = false;
                    } catch (error) {
                        console.error('Error loading reciters:', error);
                        this.loading = false;
                    }
                },
                
                goToReciter(reciter) {
                    // Navigate to reciter page with URL parameters
                    const params = new URLSearchParams({
                        id: reciter.id,
                        name: reciter.name,
                        country: reciter.country
                    });
                    window.location.href = `reciter.html?${params.toString()}`;
                }
            }
        }
    </script>
</body>
</html>
