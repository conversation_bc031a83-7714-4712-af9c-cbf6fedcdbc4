<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quran Reciter - Surahs</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-green-50 to-blue-50 min-h-screen">
    <div x-data="reciterApp()" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8">
            <!-- Back Button -->
            <button @click="goBack()" class="mb-6 flex items-center text-green-600 hover:text-green-800 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Reciters
            </button>
            
            <!-- Reciter Info -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex items-center">
                    <div class="w-20 h-20 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center mr-6">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800" x-text="reciterName"></h1>
                        <p class="text-gray-600" x-text="reciterCountry"></p>
                        <p class="text-sm text-gray-500 mt-1">114 Surahs Available</p>
                    </div>
                </div>
            </div>
        </header>

        <!-- Audio Player (Fixed at top when playing) -->
        <div x-show="currentAudio.playing" 
             class="fixed top-0 left-0 right-0 bg-white shadow-lg z-50 p-4 border-b">
            <div class="container mx-auto flex items-center justify-between">
                <div class="flex items-center">
                    <button @click="togglePlay()" class="bg-green-600 hover:bg-green-700 text-white p-2 rounded-full mr-4">
                        <i :class="currentAudio.isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
                    </button>
                    <div>
                        <p class="font-semibold text-gray-800" x-text="currentAudio.surahName"></p>
                        <p class="text-sm text-gray-600" x-text="reciterName"></p>
                    </div>
                </div>
                <button @click="stopAudio()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <!-- Progress Bar -->
            <div class="container mx-auto mt-2">
                <div class="bg-gray-200 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full transition-all duration-300" 
                         :style="`width: ${currentAudio.progress}%`"></div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div x-show="loading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600"></div>
        </div>

        <!-- Surahs List -->
        <div x-show="!loading" class="space-y-4" :class="currentAudio.playing ? 'mt-24' : ''">
            <template x-for="surah in surahs" :key="surah.number">
                <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 p-4">
                    <div class="flex items-center justify-between">
                        <!-- Surah Info -->
                        <div class="flex items-center flex-1">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-green-600 font-bold" x-text="surah.number"></span>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-800" x-text="surah.englishName"></h3>
                                <p class="text-gray-600" x-text="surah.name"></p>
                                <p class="text-sm text-gray-500">
                                    <span x-text="surah.numberOfAyahs"></span> verses • 
                                    <span x-text="surah.revelationType"></span>
                                </p>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="flex items-center space-x-2">
                            <!-- Play Button -->
                            <button @click="playAudio(surah)" 
                                    :disabled="loadingAudio === surah.number"
                                    class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white p-3 rounded-full transition-colors">
                                <i x-show="loadingAudio !== surah.number" class="fas fa-play"></i>
                                <i x-show="loadingAudio === surah.number" class="fas fa-spinner fa-spin"></i>
                            </button>
                            
                            <!-- Download Button -->
                            <button @click="downloadAudio(surah)" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full transition-colors">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Audio Element -->
        <audio x-ref="audioPlayer" @ended="onAudioEnded()" @timeupdate="updateProgress()"></audio>
    </div>

    <script>
        function reciterApp() {
            return {
                loading: true,
                loadingAudio: null,
                reciterName: '',
                reciterCountry: '',
                reciterId: '',
                surahs: [],
                currentAudio: {
                    playing: false,
                    isPlaying: false,
                    surahName: '',
                    progress: 0
                },
                
                init() {
                    this.getReciterInfo();
                    this.loadSurahs();
                },
                
                getReciterInfo() {
                    const urlParams = new URLSearchParams(window.location.search);
                    this.reciterId = urlParams.get('id') || 'ar.alafasy';
                    this.reciterName = urlParams.get('name') || 'Unknown Reciter';
                    this.reciterCountry = urlParams.get('country') || '';
                },
                
                async loadSurahs() {
                    try {
                        // Load surah information
                        const response = await fetch('https://api.alquran.cloud/v1/surah');
                        const data = await response.json();
                        
                        if (data.code === 200) {
                            this.surahs = data.data;
                        }
                        
                        this.loading = false;
                    } catch (error) {
                        console.error('Error loading surahs:', error);
                        this.loading = false;
                    }
                },
                
                async playAudio(surah) {
                    try {
                        this.loadingAudio = surah.number;
                        
                        // Construct audio URL using the API
                        const audioUrl = `https://cdn.islamic.network/quran/audio-surah/128/${this.reciterId}/${surah.number}.mp3`;
                        
                        // Stop current audio if playing
                        this.stopAudio();
                        
                        // Set new audio
                        this.$refs.audioPlayer.src = audioUrl;
                        
                        // Play audio
                        await this.$refs.audioPlayer.play();
                        
                        this.currentAudio = {
                            playing: true,
                            isPlaying: true,
                            surahName: surah.englishName,
                            progress: 0
                        };
                        
                        this.loadingAudio = null;
                    } catch (error) {
                        console.error('Error playing audio:', error);
                        alert('Error playing audio. Please try again.');
                        this.loadingAudio = null;
                    }
                },
                
                togglePlay() {
                    if (this.currentAudio.isPlaying) {
                        this.$refs.audioPlayer.pause();
                        this.currentAudio.isPlaying = false;
                    } else {
                        this.$refs.audioPlayer.play();
                        this.currentAudio.isPlaying = true;
                    }
                },
                
                stopAudio() {
                    this.$refs.audioPlayer.pause();
                    this.$refs.audioPlayer.currentTime = 0;
                    this.currentAudio = {
                        playing: false,
                        isPlaying: false,
                        surahName: '',
                        progress: 0
                    };
                },
                
                downloadAudio(surah) {
                    const audioUrl = `https://cdn.islamic.network/quran/audio-surah/128/${this.reciterId}/${surah.number}.mp3`;
                    const link = document.createElement('a');
                    link.href = audioUrl;
                    link.download = `${surah.englishName} - ${this.reciterName}.mp3`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                },
                
                updateProgress() {
                    if (this.$refs.audioPlayer.duration) {
                        this.currentAudio.progress = (this.$refs.audioPlayer.currentTime / this.$refs.audioPlayer.duration) * 100;
                    }
                },
                
                onAudioEnded() {
                    this.stopAudio();
                },
                
                goBack() {
                    window.location.href = 'index.html';
                }
            }
        }
    </script>
</body>
</html>
