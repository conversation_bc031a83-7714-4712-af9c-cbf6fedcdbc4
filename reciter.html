<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قارئ القرآن - السور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@400;600;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Amiri', sans-serif;
        }

        .arabic-text {
            font-family: '<PERSON><PERSON>', serif;
        }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        /* Pulse animation for playing indicator */
        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }

            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .pulse-ring {
            animation: pulse-ring 1.5s infinite;
        }

        /* Gradient text animation */
        @keyframes gradient-shift {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        .gradient-text {
            background: linear-gradient(-45deg, #10b981, #3b82f6, #8b5cf6, #10b981);
            background-size: 400% 400%;
            animation: gradient-shift 3s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>

<body class="bg-gradient-to-bl from-green-50 to-blue-50 min-h-screen">
    <div x-data="reciterApp()" class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="mb-8">
            <!-- Back Button -->
            <button @click="goBack()"
                class="mb-6 flex items-center text-green-600 hover:text-green-800 transition-colors">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة إلى القراء
            </button>

            <!-- Reciter Info -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex items-center">
                    <div
                        class="w-20 h-20 rounded-full bg-gradient-to-r from-green-400 to-blue-500 flex items-center justify-center ml-6">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-800 arabic-text" x-text="reciterArabicName"></h1>
                        <p class="text-gray-600" x-text="reciterName"></p>
                        <p class="text-gray-600" x-text="reciterCountry"></p>
                        <p class="text-sm text-gray-500 mt-1">١١٤ سورة متاحة</p>
                    </div>
                </div>
            </div>
        </header>

        <!-- Audio Player (Fixed at top when playing) -->
        <div x-show="currentAudio.playing" class="fixed top-0 left-0 right-0 bg-white shadow-lg z-50 p-4 border-b">
            <div class="container mx-auto flex items-center justify-between">
                <div class="flex items-center">
                    <button @click="togglePlay()"
                        class="bg-green-600 hover:bg-green-700 text-white p-2 rounded-full ml-4">
                        <i :class="currentAudio.isPlaying ? 'fas fa-pause' : 'fas fa-play'"></i>
                    </button>
                    <div>
                        <p class="font-semibold text-gray-800 arabic-text" x-text="currentAudio.surahArabicName"></p>
                        <p class="text-sm text-gray-600" x-text="currentAudio.surahName"></p>
                        <p class="text-sm text-gray-600 arabic-text" x-text="reciterArabicName"></p>
                    </div>
                </div>
                <button @click="stopAudio()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <!-- Progress Bar -->
            <div class="container mx-auto mt-2">
                <div class="bg-gray-200 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full transition-all duration-300"
                        :style="`width: ${currentAudio.progress}%`"></div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div x-show="loading" class="flex justify-center items-center py-20">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600"></div>
            <p class="mr-4 text-gray-600">جاري تحميل السور...</p>
        </div>

        <!-- Surahs List -->
        <div x-show="!loading" class="space-y-3" :class="currentAudio.playing ? 'mt-24' : ''">
            <template x-for="(surah, index) in surahs" :key="surah.number">
                <div class="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100 overflow-hidden animate-fade-in-up"
                    :style="`animation-delay: ${index * 0.1}s`">
                    <!-- Surah Header -->
                    <div class="bg-gradient-to-r from-green-50 to-blue-50 px-6 py-4 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <!-- Surah Number Badge -->
                                <div class="relative">
                                    <div
                                        class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg ml-4">
                                        <span class="text-white font-bold text-lg" x-text="surah.number"></span>
                                    </div>
                                    <!-- Playing indicator -->
                                    <div x-show="currentAudio.playing && currentAudio.surahName === surah.englishName"
                                        class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse shadow-lg">
                                        <div
                                            class="w-full h-full bg-gradient-to-r from-red-400 to-pink-400 rounded-full pulse-ring">
                                        </div>
                                        <div
                                            class="absolute inset-1 bg-white rounded-full flex items-center justify-center">
                                            <i class="fas fa-volume-up text-red-500 text-xs"></i>
                                        </div>
                                    </div>
                                </div>

                                <!-- Surah Names -->
                                <div class="flex-1">
                                    <h3 class="text-xl font-bold mb-1 arabic-text"
                                        :class="currentAudio.playing && currentAudio.surahName === surah.englishName ? 'gradient-text' : 'text-gray-800'"
                                        x-text="surah.name">
                                    </h3>
                                    <p class="text-gray-600 font-medium" x-text="surah.englishName"></p>
                                </div>
                            </div>

                            <!-- Surah Stats -->
                            <div class="text-left">
                                <div class="flex items-center text-sm text-gray-600 mb-1">
                                    <i class="fas fa-book-open ml-2 text-green-600"></i>
                                    <span x-text="surah.numberOfAyahs"></span>
                                    <span class="mr-1">آية</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-map-marker-alt ml-2 text-blue-600"></i>
                                    <span class="px-2 py-1 rounded-full text-xs font-medium"
                                        :class="surah.revelationType === 'Meccan' ? 'bg-amber-100 text-amber-800' : 'bg-emerald-100 text-emerald-800'"
                                        x-text="surah.revelationType === 'Meccan' ? 'مكية' : 'مدنية'"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="px-6 py-4 bg-gray-50">
                        <div class="flex items-center justify-center space-x-reverse space-x-4">
                            <!-- Play/Pause Button -->
                            <button @click="playAudio(surah)" :disabled="loadingAudio === surah.number"
                                class="flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-md"
                                :class="loadingAudio === surah.number ?
                                        'bg-gray-400 text-white cursor-not-allowed' :
                                        'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-green-200'">
                                <i x-show="loadingAudio !== surah.number" class="fas fa-play ml-2"></i>
                                <i x-show="loadingAudio === surah.number" class="fas fa-spinner fa-spin ml-2"></i>
                                <span x-text="loadingAudio === surah.number ? 'جاري التحميل...' : 'تشغيل'"></span>
                            </button>

                            <!-- Download Button -->
                            <button @click="downloadAudio(surah)" :disabled="downloadingAudio === surah.number"
                                class="flex items-center px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-md shadow-blue-200"
                                :class="downloadingAudio === surah.number ?
                                        'bg-gray-400 text-white cursor-not-allowed' :
                                        'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white'">
                                <i x-show="downloadingAudio !== surah.number" class="fas fa-download ml-2"></i>
                                <i x-show="downloadingAudio === surah.number" class="fas fa-spinner fa-spin ml-2"></i>
                                <span x-text="downloadingAudio === surah.number ? 'جاري التحميل...' : 'تحميل'"></span>
                            </button>

                            <!-- Info Button -->
                            <button @click="showSurahInfo(surah)"
                                class="flex items-center px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-md shadow-purple-200">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>

                        <!-- Progress Bar for Currently Playing -->
                        <div x-show="currentAudio.playing && currentAudio.surahName === surah.englishName"
                            class="mt-4 bg-gray-200 rounded-full h-2 overflow-hidden">
                            <div class="bg-gradient-to-r from-green-400 to-green-500 h-full rounded-full transition-all duration-300"
                                :style="`width: ${currentAudio.progress}%`"></div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Audio Element -->
        <audio x-ref="audioPlayer" @ended="onAudioEnded()" @timeupdate="updateProgress()"></audio>

        <!-- Surah Info Modal -->
        <div x-show="showingInfo" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
            @click="closeSurahInfo()">

            <div x-show="showingInfo" x-transition:enter="transition ease-out duration-300"
                x-transition:enter-start="opacity-0 transform scale-95"
                x-transition:enter-end="opacity-100 transform scale-100"
                x-transition:leave="transition ease-in duration-200"
                x-transition:leave-start="opacity-100 transform scale-100"
                x-transition:leave-end="opacity-0 transform scale-95"
                class="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-96 overflow-y-auto" @click.stop>

                <!-- Modal Header -->
                <div class="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6 rounded-t-2xl">
                    <div class="flex items-center justify-between">
                        <h3 class="text-xl font-bold arabic-text" x-text="selectedSurah?.name"></h3>
                        <button @click="closeSurahInfo()" class="text-white hover:text-gray-200 transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <p class="text-green-100 mt-1" x-text="selectedSurah?.englishName"></p>
                </div>

                <!-- Modal Content -->
                <div class="p-6">
                    <div class="space-y-4">
                        <!-- Surah Number -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-600">رقم السورة:</span>
                            <span class="font-bold text-green-600" x-text="selectedSurah?.number"></span>
                        </div>

                        <!-- Number of Ayahs -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-600">عدد الآيات:</span>
                            <span class="font-bold text-blue-600" x-text="selectedSurah?.numberOfAyahs"></span>
                        </div>

                        <!-- Revelation Type -->
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-600">نوع السورة:</span>
                            <span class="px-3 py-1 rounded-full text-sm font-medium"
                                :class="selectedSurah?.revelationType === 'Meccan' ? 'bg-amber-100 text-amber-800' : 'bg-emerald-100 text-emerald-800'"
                                x-text="selectedSurah?.revelationType === 'Meccan' ? 'مكية' : 'مدنية'"></span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-reverse space-x-3 pt-4">
                            <button @click="playAudio(selectedSurah); closeSurahInfo()"
                                class="flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200">
                                <i class="fas fa-play ml-2"></i>
                                تشغيل
                            </button>
                            <button @click="downloadAudio(selectedSurah); closeSurahInfo()"
                                class="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200">
                                <i class="fas fa-download ml-2"></i>
                                تحميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function reciterApp() {
            return {
                loading: true,
                loadingAudio: null,
                reciterName: '',
                reciterArabicName: '',
                reciterCountry: '',
                reciterId: '',
                surahs: [],
                currentAudio: {
                    playing: false,
                    isPlaying: false,
                    surahName: '',
                    surahArabicName: '',
                    progress: 0
                },
                downloadingAudio: null,
                showingInfo: false,
                selectedSurah: null,

                init() {
                    this.getReciterInfo();
                    this.loadSurahs();
                },

                getReciterInfo() {
                    const urlParams = new URLSearchParams(window.location.search);
                    this.reciterId = urlParams.get('id') || '123'; // Default to Mishary Al-Afasy
                    this.reciterName = urlParams.get('name') || 'Unknown Reciter';
                    this.reciterArabicName = urlParams.get('arabicName') || 'قارئ غير معروف';
                    this.reciterCountry = urlParams.get('country') || '';
                },

                async loadSurahs() {
                    try {
                        // Load surah information from mp3quran.net API
                        const response = await fetch('https://mp3quran.net/api/v3/suwar?language=ar');
                        const data = await response.json();

                        if (data.suwar) {
                            // Transform the data to match our expected format
                            this.surahs = data.suwar.map(surah => ({
                                number: surah.id,
                                name: surah.name,
                                englishName: this.getEnglishSurahName(surah.id),
                                numberOfAyahs: this.getAyahCount(surah.id),
                                revelationType: surah.makkia === 1 ? 'Meccan' : 'Medinan'
                            }));
                        }

                        this.loading = false;
                    } catch (error) {
                        console.error('Error loading surahs:', error);
                        this.loading = false;
                    }
                },

                getEnglishSurahName(surahId) {
                    const englishNames = {
                        1: 'Al-Fatihah', 2: 'Al-Baqarah', 3: 'Ali \'Imran', 4: 'An-Nisa', 5: 'Al-Ma\'idah',
                        6: 'Al-An\'am', 7: 'Al-A\'raf', 8: 'Al-Anfal', 9: 'At-Tawbah', 10: 'Yunus',
                        11: 'Hud', 12: 'Yusuf', 13: 'Ar-Ra\'d', 14: 'Ibrahim', 15: 'Al-Hijr',
                        16: 'An-Nahl', 17: 'Al-Isra', 18: 'Al-Kahf', 19: 'Maryam', 20: 'Taha',
                        21: 'Al-Anbya', 22: 'Al-Hajj', 23: 'Al-Mu\'minun', 24: 'An-Nur', 25: 'Al-Furqan',
                        26: 'Ash-Shu\'ara', 27: 'An-Naml', 28: 'Al-Qasas', 29: 'Al-\'Ankabut', 30: 'Ar-Rum',
                        31: 'Luqman', 32: 'As-Sajdah', 33: 'Al-Ahzab', 34: 'Saba', 35: 'Fatir',
                        36: 'Ya-Sin', 37: 'As-Saffat', 38: 'Sad', 39: 'Az-Zumar', 40: 'Ghafir',
                        41: 'Fussilat', 42: 'Ash-Shuraa', 43: 'Az-Zukhruf', 44: 'Ad-Dukhan', 45: 'Al-Jathiyah',
                        46: 'Al-Ahqaf', 47: 'Muhammad', 48: 'Al-Fath', 49: 'Al-Hujurat', 50: 'Qaf',
                        51: 'Adh-Dhariyat', 52: 'At-Tur', 53: 'An-Najm', 54: 'Al-Qamar', 55: 'Ar-Rahman',
                        56: 'Al-Waqi\'ah', 57: 'Al-Hadid', 58: 'Al-Mujadila', 59: 'Al-Hashr', 60: 'Al-Mumtahanah',
                        61: 'As-Saff', 62: 'Al-Jumu\'ah', 63: 'Al-Munafiqun', 64: 'At-Taghabun', 65: 'At-Talaq',
                        66: 'At-Tahrim', 67: 'Al-Mulk', 68: 'Al-Qalam', 69: 'Al-Haqqah', 70: 'Al-Ma\'arij',
                        71: 'Nuh', 72: 'Al-Jinn', 73: 'Al-Muzzammil', 74: 'Al-Muddaththir', 75: 'Al-Qiyamah',
                        76: 'Al-Insan', 77: 'Al-Mursalat', 78: 'An-Naba', 79: 'An-Nazi\'at', 80: 'Abasa',
                        81: 'At-Takwir', 82: 'Al-Infitar', 83: 'Al-Mutaffifin', 84: 'Al-Inshiqaq', 85: 'Al-Buruj',
                        86: 'At-Tariq', 87: 'Al-A\'la', 88: 'Al-Ghashiyah', 89: 'Al-Fajr', 90: 'Al-Balad',
                        91: 'Ash-Shams', 92: 'Al-Layl', 93: 'Ad-Duhaa', 94: 'Ash-Sharh', 95: 'At-Tin',
                        96: 'Al-\'Alaq', 97: 'Al-Qadr', 98: 'Al-Bayyinah', 99: 'Az-Zalzalah', 100: 'Al-\'Adiyat',
                        101: 'Al-Qari\'ah', 102: 'At-Takathur', 103: 'Al-\'Asr', 104: 'Al-Humazah', 105: 'Al-Fil',
                        106: 'Quraysh', 107: 'Al-Ma\'un', 108: 'Al-Kawthar', 109: 'Al-Kafirun', 110: 'An-Nasr',
                        111: 'Al-Masad', 112: 'Al-Ikhlas', 113: 'Al-Falaq', 114: 'An-Nas'
                    };
                    return englishNames[surahId] || `Surah ${surahId}`;
                },

                getAyahCount(surahId) {
                    const ayahCounts = {
                        1: 7, 2: 286, 3: 200, 4: 176, 5: 120, 6: 165, 7: 206, 8: 75, 9: 129, 10: 109,
                        11: 123, 12: 111, 13: 43, 14: 52, 15: 99, 16: 128, 17: 111, 18: 110, 19: 98, 20: 135,
                        21: 112, 22: 78, 23: 118, 24: 64, 25: 77, 26: 227, 27: 93, 28: 88, 29: 69, 30: 60,
                        31: 34, 32: 30, 33: 73, 34: 54, 35: 45, 36: 83, 37: 182, 38: 88, 39: 75, 40: 85,
                        41: 54, 42: 53, 43: 89, 44: 59, 45: 37, 46: 35, 47: 38, 48: 29, 49: 18, 50: 45,
                        51: 60, 52: 49, 53: 62, 54: 55, 55: 78, 56: 96, 57: 29, 58: 22, 59: 24, 60: 13,
                        61: 14, 62: 11, 63: 11, 64: 18, 65: 12, 66: 12, 67: 30, 68: 52, 69: 52, 70: 44,
                        71: 28, 72: 28, 73: 20, 74: 56, 75: 40, 76: 31, 77: 50, 78: 40, 79: 46, 80: 42,
                        81: 29, 82: 19, 83: 36, 84: 25, 85: 22, 86: 17, 87: 19, 88: 26, 89: 30, 90: 20,
                        91: 15, 92: 21, 93: 11, 94: 8, 95: 8, 96: 19, 97: 5, 98: 8, 99: 8, 100: 11,
                        101: 11, 102: 8, 103: 3, 104: 9, 105: 5, 106: 4, 107: 7, 108: 3, 109: 6, 110: 3,
                        111: 5, 112: 4, 113: 5, 114: 6
                    };
                    return ayahCounts[surahId] || 0;
                },

                async playAudio(surah) {
                    try {
                        this.loadingAudio = surah.number;

                        // Get reciter data to find the correct moshaf and server
                        const reciterResponse = await fetch(`https://mp3quran.net/api/v3/reciters?language=ar&reciter=${this.reciterId}`);
                        const reciterData = await reciterResponse.json();

                        let audioUrl = '';

                        if (reciterData.reciters && reciterData.reciters.length > 0) {
                            const reciter = reciterData.reciters[0];
                            // Find the main moshaf (usually the first one with type 11 - Hafs)
                            const mainMoshaf = reciter.moshaf.find(m => m.moshaf_type === 11) || reciter.moshaf[0];

                            if (mainMoshaf) {
                                // Check if this surah is available in the moshaf
                                const surahList = mainMoshaf.surah_list.split(',').map(s => parseInt(s));
                                if (surahList.includes(surah.number)) {
                                    // Construct audio URL using mp3quran.net structure
                                    const paddedSurahNumber = surah.number.toString().padStart(3, '0');
                                    audioUrl = `${mainMoshaf.server}${paddedSurahNumber}.mp3`;
                                } else {
                                    throw new Error('هذه السورة غير متاحة لهذا القارئ');
                                }
                            }
                        }

                        if (!audioUrl) {
                            throw new Error('لم يتم العثور على ملف الصوت');
                        }

                        // Stop current audio if playing
                        this.stopAudio();

                        // Set new audio
                        this.$refs.audioPlayer.src = audioUrl;

                        // Play audio
                        await this.$refs.audioPlayer.play();

                        this.currentAudio = {
                            playing: true,
                            isPlaying: true,
                            surahName: surah.englishName,
                            surahArabicName: surah.name,
                            progress: 0
                        };

                        this.loadingAudio = null;
                    } catch (error) {
                        console.error('Error playing audio:', error);
                        alert(error.message || 'خطأ في تشغيل الصوت. يرجى المحاولة مرة أخرى.');
                        this.loadingAudio = null;
                    }
                },

                togglePlay() {
                    if (this.currentAudio.isPlaying) {
                        this.$refs.audioPlayer.pause();
                        this.currentAudio.isPlaying = false;
                    } else {
                        this.$refs.audioPlayer.play();
                        this.currentAudio.isPlaying = true;
                    }
                },

                stopAudio() {
                    this.$refs.audioPlayer.pause();
                    this.$refs.audioPlayer.currentTime = 0;
                    this.currentAudio = {
                        playing: false,
                        isPlaying: false,
                        surahName: '',
                        surahArabicName: '',
                        progress: 0
                    };
                },

                async downloadAudio(surah) {
                    try {
                        this.downloadingAudio = surah.number;

                        // Get reciter data to find the correct moshaf and server
                        const reciterResponse = await fetch(`https://mp3quran.net/api/v3/reciters?language=ar&reciter=${this.reciterId}`);
                        const reciterData = await reciterResponse.json();

                        let audioUrl = '';

                        if (reciterData.reciters && reciterData.reciters.length > 0) {
                            const reciter = reciterData.reciters[0];
                            // Find the main moshaf (usually the first one with type 11 - Hafs)
                            const mainMoshaf = reciter.moshaf.find(m => m.moshaf_type === 11) || reciter.moshaf[0];

                            if (mainMoshaf) {
                                // Check if this surah is available in the moshaf
                                const surahList = mainMoshaf.surah_list.split(',').map(s => parseInt(s));
                                if (surahList.includes(surah.number)) {
                                    // Construct audio URL using mp3quran.net structure
                                    const paddedSurahNumber = surah.number.toString().padStart(3, '0');
                                    audioUrl = `${mainMoshaf.server}${paddedSurahNumber}.mp3`;
                                }
                            }
                        }

                        if (audioUrl) {
                            // Show success message
                            this.showNotification('بدء التحميل...', 'success');

                            const link = document.createElement('a');
                            link.href = audioUrl;
                            link.download = `${surah.name} - ${this.reciterArabicName}.mp3`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            // Show completion message after a delay
                            setTimeout(() => {
                                this.showNotification('تم بدء التحميل بنجاح', 'success');
                            }, 1000);
                        } else {
                            this.showNotification('هذه السورة غير متاحة للتحميل من هذا القارئ', 'error');
                        }

                        this.downloadingAudio = null;
                    } catch (error) {
                        console.error('Error downloading audio:', error);
                        this.showNotification('خطأ في تحميل الملف. يرجى المحاولة مرة أخرى.', 'error');
                        this.downloadingAudio = null;
                    }
                },

                showSurahInfo(surah) {
                    this.selectedSurah = surah;
                    this.showingInfo = true;
                },

                closeSurahInfo() {
                    this.showingInfo = false;
                    this.selectedSurah = null;
                },

                showNotification(message, type = 'info') {
                    // Create notification element
                    const notification = document.createElement('div');
                    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

                    if (type === 'success') {
                        notification.className += ' bg-green-500 text-white';
                    } else if (type === 'error') {
                        notification.className += ' bg-red-500 text-white';
                    } else {
                        notification.className += ' bg-blue-500 text-white';
                    }

                    notification.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} ml-2"></i>
                            <span>${message}</span>
                        </div>
                    `;

                    document.body.appendChild(notification);

                    // Animate in
                    setTimeout(() => {
                        notification.classList.remove('translate-x-full');
                    }, 100);

                    // Remove after 3 seconds
                    setTimeout(() => {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            document.body.removeChild(notification);
                        }, 300);
                    }, 3000);
                },

                updateProgress() {
                    if (this.$refs.audioPlayer.duration) {
                        this.currentAudio.progress = (this.$refs.audioPlayer.currentTime / this.$refs.audioPlayer.duration) * 100;
                    }
                },

                onAudioEnded() {
                    this.stopAudio();
                },

                goBack() {
                    window.location.href = 'index.html';
                }
            }
        }
    </script>
</body>

</html>